<style>
h1, h2, h3, h4, p, li, span, td {
  font-family: "微软雅黑", "Microsoft YaHei", "苹方", "PingFang SC", sans-serif !important;
}
h2 {
  font-size: 1.4em !important;
  margin-bottom: 4px !important;
  margin-top: 7px !important;
}
h3 {
  font-size: 1.2em !important;
  margin-bottom: 1px !important;
  margin-top: 4px !important;
}
h4 {
  font-size: 1.1em !important;
  margin-bottom: 1px !important;
  margin-top: 1px !important;
}
p, li, span {
  font-size: 0.95em !important;
  line-height: 1.25 !important;
  margin-bottom: 2px !important;
  margin-top: 2px !important;
}
div {
  margin: 2px 0 !important;
  padding-bottom: 2px !important;
}
ul {
  margin-top: 3px !important;
  margin-bottom: 3px !important;
  padding-top: 0 !important;
}
strong {
  font-weight: 550;

}
</style>

<h1 style="display: flex; align-items: center; color: #6aa0f0; margin-bottom: 15px; margin-top: 20px;">
  <span style="margin-right: 10px;">👩‍💻</span> 林雨 | 前端开发工程师
</h1>

<table style="border-collapse: separate; border-spacing: 0 3px; width: 100%; margin-bottom: 8px; border: none;">
  <tr>
    <td style="padding: 0px 0; font-size: 1em; border: none;"><strong>求职意向</strong>：前端开发工程师</td>
  </tr>
  <tr>
    <td style="padding: 0px 0; font-size: 1em; border: none;"><strong>教育背景</strong>：西华大学 | 物联网工程（2022.09—2026.06）</td>
  </tr>
  <tr>
    <td style="padding: 0px 0; font-size: 1em; border: none;"><strong>主修课程</strong>：Web前/后端开发技术、数据结构与算法设计、计算机网络、数据库原理与应用</td>
  </tr>
  <tr>
    <td style="padding: 0px 0; font-size: 1em; border: none;"><strong>联系方式</strong>：(+86) 177-8181-1549 | <EMAIL></td>
  </tr>

  <tr>
    <td style="padding: 0px 0; font-size: 1em; border: none;"><strong>Github</strong>：https://github.com/Tully-L</td>
  </tr>
    <tr>
    <td style="padding: 0px 0; font-size: 1em; border: none;"><strong>个人网站</strong>：https://tully.top/</td>
  </tr>
</table>


<h2 style="display: flex; align-items: center; color: #6aa0f0; margin-bottom: 15px; margin-top: 25px;">
  <span style="margin-right: 10px;">🛠️</span> 专业技能
</h2>

- 前端开发：React、Vue.js、TypeScript、Less/CSS3、HTML5
- 开发框架：Uniapp、Node.js、Express、Flask、MySQL、VSCode
- 开发工具：VSCode、PlatformIO、Keil、PyCharm
- 其他技能：微信小程序开发、STM32/ESP32嵌入式开发

<h2 style="display: flex; align-items: center; color: #6aa0f0; margin-bottom: 10px;">
  <span style="margin-right: 10px;">🌟</span> 核心项目经验 
</h2>

<h3><span style="color: #8aadf4; display: flex; align-items: center;"><span style="margin-right: 8px;">📱</span> 小程序开发<span style="font-size: 0.85em; font-weight: normal; margin-left: 8px; color: #666;">【独立开发】</span></span></h3>

<div style="padding-left: 2em;">
  <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;">
    <h4 style="margin: 0;">生鲜商城小程序</h4>
    <span style="font-weight: normal; color: #666; font-size: 0.9em;">2025.03-2025.04</span>
  </div>

  - <strong>技术栈</strong>：Uniapp、TS+Less、Vue、微信开发者工具
  - <strong>功能实现</strong>：商品分类展示、购物车管理、订单处理、用户登录注册、配送时间选择
  - <strong>技术亮点</strong>：组件化开发、状态管理清晰、页面过渡流畅、支持多端部署
  - <strong>项目成果</strong>：独立完成100%前端开发，提前7天完成5个核心页面；50+用户测试，UI满意度85%；深入理解Uniapp跨端开发与Vue组件通信
  - <strong>场景价值</strong>：为家庭生鲜配送定制，解决手写记账效率低、易出错问题。通过小程序实现自动记账和订单汇总，预计日处理订单200+，单笔订单处理时间从5分钟缩短至30秒，效率提升90%
</div>

<h3><span style="color: #8aadf4; display: flex; align-items: center;"><span style="margin-right: 8px;">💻</span> 网页开发项目<span style="font-size: 0.85em; font-weight: normal; margin-left: 8px; color: #666;">【独立开发】</span></span></h3>

<div style="padding-left: 2em;">
  <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;">
    <h4 style="margin: 0;">交互式校园信息展示网站</h4>
    <span style="font-weight: normal; color: #666; font-size: 0.9em;">2024.10-2024.12</span>
  </div>

  - <strong>技术栈</strong>：React、TypeScript、Three.js、Leaflet、Tailwind CSS、Vite、Framer Motion
  - <strong>功能实现</strong>：3D校园漫游、交互式地图导航、多校区切换、社区照片分享
  - <strong>技术亮点</strong>：Three.js 3D地球可视化、智能导航系统、动态交互体验、全终端响应式设计
  - <strong>项目成果</strong>：完全独立开发（对接客户到部署上线），Three.js 3D渲染优化，移动端适配率100%；深入理解React组件化开发与Three.js 3D渲染
  - <strong>场景价值</strong>：重构校园信息获取方式，解决传统导览效率低、信息分散问题。通过3D可视化和智能导航，信息查找效率提升80%，日均访问200+，用户满意度95%
</div>

<h3><span style="color: #8aadf4; display: flex; align-items: center;"><span style="margin-right: 8px;">🎾</span> 网球热赛事管理小程序<span style="font-size: 0.85em; font-weight: normal; margin-left: 8px; color: #666;">【全栈开发】</span></span></h3>

<div style="padding-left: 2em;">
  <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;">
    <h4 style="margin: 0;">网球热赛事管理小程序</h4>
    <span style="font-weight: normal; color: #666; font-size: 0.9em;">2024.08-2024.10</span>
  </div>

  - <strong>技术栈</strong>：微信小程序、Node.js、Express.js、MongoDB、Socket.io、JWT、微信支付API
  - <strong>功能实现</strong>：赛事发布、用户认证、实时比分、支付报名、社交分享
  - <strong>技术亮点</strong>：Socket.io毫秒级比分同步、智能赛事推荐引擎、全流程自动化、多端通知体系
  - <strong>项目成果</strong>：独立完成100%全栈开发，提前5天交付8个核心页面；30+用户测试UI满意度90%；深入理解微信小程序开发与Node.js后端架构
  - <strong>场景价值</strong>：重构网球赛事管理流程，解决传统模式效率低、信息不透明行业痛点。通过自动化管理，单场赛事管理时间从2小时缩短至15分钟，效率提升87%，支持日均50+赛事规模
</div>

<h3><span style="color: #8aadf4; display: flex; align-items: center;"><span style="margin-right: 8px;">📚</span> 学生积分管理小程序<span style="font-size: 0.85em; font-weight: normal; margin-left: 8px; color: #666;">【全栈开发】</span></span></h3>

<div style="padding-left: 2em;">
  <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;">
    <h4 style="margin: 0;">学生积分管理小程序</h4>
    <span style="font-weight: normal; color: #666; font-size: 0.9em;">2024.05-2024.07</span>
  </div>

  - <strong>技术栈</strong>：微信小程序、Node.js、Express.js、MongoDB、JWT、Mongoose、微信云存储
  - <strong>功能实现</strong>：用户认证、多媒体作业管理、智能积分统计、双角色权限控制
  - <strong>技术亮点</strong>：4K多媒体作业提交、动态积分激励系统、双角色权限架构、数据化成长档案
  - <strong>项目成果</strong>：独立完成100%全栈开发，8个核心页面零BUG交付，支持双角色协同；深入理解微信小程序云存储与MongoDB数据库设计
  - <strong>场景价值</strong>：解决传统作业管理效率低、家校沟通断层、激励机制缺失问题。通过数字化管理，日均处理作业100+，审核效率提升83%，学习主动性提升40%
</div>

<h2 style="display: flex; align-items: center; color: #6aa0f0; margin-bottom: 10px;">
  <span style="margin-right: 10px;">🏆</span> 荣誉与证书
</h2>

<div style="padding-left: 1em;">
  <p style="margin-bottom: 8px; color: #333; font-size: 0.95em; line-height: 1.4;">
    <span style="font-weight: 600; color: #333;">技能证书：</span>工业互联网平台开发工程师（初级）、CET-6（英语六级）、普通话国家二级甲等证书
  </p>
  
  <p style="margin-bottom: 8px; color: #333; font-size: 0.95em; line-height: 1.4;">
    <span style="font-weight: 600; color: #333;">获奖经历：</span>中国大学生计算机设计大赛省二（2025.5）、蓝桥杯Web应用开发大学组三等奖（2024.4）、四川高校阅读文化节"阅读之星"（连续三年2023-2025）
  </p>
  
  <p style="margin-bottom: 8px; color: #333; font-size: 0.95em; line-height: 1.4;">
    <span style="font-weight: 600; color: #333;">校园经历：</span>担任西华大学小球协会会长（2024.9-至今）、获院级三好学生（2023.11）、"三下乡"社会实践活动优秀个人奖（2023.12）
  </p>
  

</div>

