<style>
h1, h2, h3, h4, p, li, span, td {
  font-family: "微软雅黑", "Microsoft YaHei", "苹方", "PingFang SC", sans-serif !important;
}
h2 {
  font-size: 1.4em !important;
  margin-bottom: 4px !important;
  margin-top: 7px !important;
}
h3 {
  font-size: 1.2em !important;
  margin-bottom: 1px !important;
  margin-top: 4px !important;
}
h4 {
  font-size: 1.1em !important;
  margin-bottom: 1px !important;
  margin-top: 1px !important;
}
p, li, span {
  font-size: 0.95em !important;
  line-height: 1.25 !important;
  margin-bottom: 2px !important;
  margin-top: 2px !important;
}
div {
  margin: 2px 0 !important;
  padding-bottom: 2px !important;
}
ul {
  margin-top: 3px !important;
  margin-bottom: 3px !important;
  padding-top: 0 !important;
}
strong {
  font-weight: 550;

}
</style>

<h1 style="display: flex; align-items: center; color: #6aa0f0; margin-bottom: 15px; margin-top: 20px;">
  <span style="margin-right: 10px;">👩‍💻</span> 林雨 | 前端开发工程师
</h1>

<table style="border-collapse: separate; border-spacing: 0 3px; width: 100%; margin-bottom: 8px; border: none;">
  <tr>
    <td style="padding: 0px 0; font-size: 1em; border: none;"><strong>求职意向</strong>：前端开发工程师</td>
  </tr>
  <tr>
    <td style="padding: 0px 0; font-size: 1em; border: none;"><strong>教育背景</strong>：西华大学 | 物联网工程（2022.09—2026.06）</td>
  </tr>
  <tr>
    <td style="padding: 0px 0; font-size: 1em; border: none;"><strong>主修课程</strong>：Web前/后端开发技术、数据结构与算法设计、计算机网络、数据库原理与应用</td>
  </tr>
  <tr>
    <td style="padding: 0px 0; font-size: 1em; border: none;"><strong>联系方式</strong>：(+86) 177-8181-1549 | <EMAIL></td>
  </tr>

  <tr>
    <td style="padding: 0px 0; font-size: 1em; border: none;"><strong>Github</strong>：https://github.com/Tully-L</td>
  </tr>
    <tr>
    <td style="padding: 0px 0; font-size: 1em; border: none;"><strong>个人网站</strong>：https://tully.top/</td>
  </tr>
</table>


<h2 style="display: flex; align-items: center; color: #6aa0f0; margin-bottom: 15px; margin-top: 25px;">
  <span style="margin-right: 10px;">🛠️</span> 专业技能
</h2>

- **前端开发**：JavaScript、CSS3、HTML5、TypeScript、Less/Sass
- **前端框架**：React、Vue.js、Angular（基础）- 熟练掌握React和Vue.js开发
- **后端开发**：Python、Node.js、Express
- **Python框架**：Flask、Django（基础）- 有Flask项目经验
- **数据库技术**：MySQL、PostgreSQL、MongoDB - 熟练编写SQL语句
- **开发工具**：VSCode、PyCharm、Git、微信开发者工具
- **其他技能**：微信小程序开发、RESTful API设计、响应式Web设计

<h2 style="display: flex; align-items: center; color: #6aa0f0; margin-bottom: 10px;">
  <span style="margin-right: 10px;">🌟</span> 核心项目经验 
</h2>

<h3><span style="color: #8aadf4; display: flex; align-items: center;"><span style="margin-right: 8px;">📱</span> 小程序开发<span style="font-size: 0.85em; font-weight: normal; margin-left: 8px; color: #666;">【独立开发】</span></span></h3>

<div style="padding-left: 2em;">
  <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;">
    <h4 style="margin: 0;">生鲜商城小程序</h4>
    <span style="font-weight: normal; color: #666; font-size: 0.9em;">2025.03-2025.04</span>
  </div>

  - <strong>技术栈</strong>：Vue.js、TypeScript、CSS3、HTML5、Python Flask后端、MySQL数据库
  - <strong>功能实现</strong>：商品信息管理、用户认证系统、订单数据处理、支付接口集成、数据统计分析
  - <strong>技术亮点</strong>：Vue.js组件化开发、Flask RESTful API设计、MySQL数据库优化、响应式Web界面
  - <strong>项目成果</strong>：独立完成前后端开发，Vue.js前端与Flask后端分离架构，MySQL数据库设计优化，支持高并发访问
  - <strong>场景价值</strong>：构建电商业务管理系统，解决传统手工记账效率低、数据统计困难问题。通过Web技术实现业务数字化，预计日处理订单200+，数据处理效率提升90%
</div>

<h3><span style="color: #8aadf4; display: flex; align-items: center;"><span style="margin-right: 8px;">🌍</span> 交互式校园信息展示网站<span style="font-size: 0.85em; font-weight: normal; margin-left: 8px; color: #666;">【已上线运营】</span></span></h3>

<div style="padding-left: 2em;">
  <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;">
    <h4 style="margin: 0;">基于React+Three.js的地理信息可视化平台</h4>
    <span style="font-weight: normal; color: #666; font-size: 0.9em;">2024.10-2024.12</span>
  </div>

  - <strong>技术栈</strong>：React、TypeScript、Three.js、Leaflet地图引擎、PostgreSQL、Flask后端、CSS3
  - <strong>功能实现</strong>：3D地理建模与渲染、交互式地图导航、空间数据查询、多校区地理信息管理、用户位置服务
  - <strong>技术亮点</strong>：Three.js WebGL 3D地球可视化、Leaflet地图引擎集成、PostgreSQL空间数据存储、RESTful API设计、响应式Web界面
  - <strong>项目成果</strong>：**已正式上线运营**（https://greenpulsemap.com/），完全独立开发并部署，支持多终端访问，地理数据处理性能优化90%
  - <strong>场景价值</strong>：构建数字化校园地理信息系统，解决传统纸质地图效率低、信息更新滞后问题。通过Web GIS技术实现空间数据可视化，日均访问200+用户，信息查询效率提升80%</div>

<h3><span style="color: #8aadf4; display: flex; align-items: center;"><span style="margin-right: 8px;">🎾</span> 网球热赛事管理平台<span style="font-size: 0.85em; font-weight: normal; margin-left: 8px; color: #666;">【已上线运营】</span></span></h3>

<div style="padding-left: 2em;">
  <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;">
    <h4 style="margin: 0;">基于Python Flask的全栈Web应用</h4>
    <span style="font-weight: normal; color: #666; font-size: 0.9em;">2024.08-2024.10</span>
  </div>

  - <strong>技术栈</strong>：Python、Flask、JavaScript、HTML5、CSS3、MySQL、RESTful API、JWT认证
  - <strong>功能实现</strong>：赛事信息管理、用户注册认证、实时数据同步、在线支付集成、数据统计分析
  - <strong>技术亮点</strong>：Flask MVC架构设计、MySQL数据库优化、前后端分离开发、响应式Web界面、实时数据处理
  - <strong>项目成果</strong>：**已正式上线运营**（微信搜索"网球热"），独立完成全栈开发，支持日均50+赛事管理，系统稳定性99.5%
  - <strong>场景价值</strong>：构建体育赛事数字化管理系统，解决传统人工管理效率低、数据统计困难问题。通过Web技术实现赛事全流程自动化，管理效率提升87%，服务用户1000+
</div>

<h3><span style="color: #8aadf4; display: flex; align-items: center;"><span style="margin-right: 8px;">📚</span> 学生积分管理系统<span style="font-size: 0.85em; font-weight: normal; margin-left: 8px; color: #666;">【Python全栈】</span></span></h3>

<div style="padding-left: 2em;">
  <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;">
    <h4 style="margin: 0;">基于Django的教育管理Web平台</h4>
    <span style="font-weight: normal; color: #666; font-size: 0.9em;">2024.05-2024.07</span>
  </div>

  - <strong>技术栈</strong>：Python、Django、JavaScript、HTML5、CSS3、PostgreSQL、RESTful API、JWT认证
  - <strong>功能实现</strong>：用户权限管理、数据统计分析、文件上传处理、多角色协同、报表生成
  - <strong>技术亮点</strong>：Django ORM数据建模、PostgreSQL复杂查询优化、前后端分离架构、响应式界面设计
  - <strong>项目成果</strong>：独立完成Python全栈开发，8个核心模块零BUG交付，支持多用户并发访问，数据处理性能优化60%
  - <strong>场景价值</strong>：构建教育数据管理平台，解决传统管理系统效率低、数据分析困难问题。通过Web技术实现数字化管理，日均处理数据100+条，工作效率提升83%
</div>

<h2 style="display: flex; align-items: center; color: #6aa0f0; margin-bottom: 10px;">
  <span style="margin-right: 10px;">🏆</span> 荣誉与证书
</h2>

<div style="padding-left: 1em;">
  <p style="margin-bottom: 8px; color: #333; font-size: 0.95em; line-height: 1.4;">
    <span style="font-weight: 600; color: #333;">技能证书：</span>工业互联网平台开发工程师（初级）、CET-6（英语六级）、普通话国家二级甲等证书
  </p>
  
  <p style="margin-bottom: 8px; color: #333; font-size: 0.95em; line-height: 1.4;">
    <span style="font-weight: 600; color: #333;">获奖经历：</span>中国大学生计算机设计大赛省二（2025.5）、蓝桥杯Web应用开发大学组三等奖（2024.4）、四川高校阅读文化节"阅读之星"（连续三年2023-2025）
  </p>
  
  <p style="margin-bottom: 8px; color: #333; font-size: 0.95em; line-height: 1.4;">
    <span style="font-weight: 600; color: #333;">校园经历：</span>担任西华大学小球协会会长（2024.9-至今）、获院级三好学生（2023.11）、"三下乡"社会实践活动优秀个人奖（2023.12）
  </p>
  

</div>

