<style>
h1, h2, h3, h4, p, li, span, td {
  font-family: "微软雅黑", "Microsoft YaHei", "苹方", "PingFang SC", sans-serif !important;
}
h2 {
  font-size: 1.4em !important;
  margin-bottom: 4px !important;
  margin-top: 7px !important;
}
h3 {
  font-size: 1.2em !important;
  margin-bottom: 1px !important;
  margin-top: 4px !important;
}
h4 {
  font-size: 1.1em !important;
  margin-bottom: 1px !important;
  margin-top: 1px !important;
}
p, li, span {
  font-size: 0.95em !important;
  line-height: 1.25 !important;
  margin-bottom: 2px !important;
  margin-top: 2px !important;
}
div {
  margin: 2px 0 !important;
  padding-bottom: 2px !important;
}
ul {
  margin-top: 3px !important;
  margin-bottom: 3px !important;
  padding-top: 0 !important;
}
strong {
  font-weight: 550;

}
</style>

<h1 style="display: flex; align-items: center; color: #6aa0f0; margin-bottom: 15px; margin-top: 20px;">
  <span style="margin-right: 10px;">👩‍💻</span> 林雨 | 前端开发工程师
</h1>

<table style="border-collapse: separate; border-spacing: 0 3px; width: 100%; margin-bottom: 8px; border: none;">
  <tr>
    <td style="padding: 0px 0; font-size: 1em; border: none;"><strong>求职意向</strong>：前端开发工程师</td>
  </tr>
  <tr>
    <td style="padding: 0px 0; font-size: 1em; border: none;"><strong>教育背景</strong>：西华大学 | 物联网工程（2022.09—2026.06）</td>
  </tr>
  <tr>
    <td style="padding: 0px 0; font-size: 1em; border: none;"><strong>主修课程</strong>：Web前/后端开发技术、数据结构与算法设计、计算机网络、数据库原理与应用</td>
  </tr>
  <tr>
    <td style="padding: 0px 0; font-size: 1em; border: none;"><strong>联系方式</strong>：(+86) 177-8181-1549 | <EMAIL></td>
  </tr>

  <tr>
    <td style="padding: 0px 0; font-size: 1em; border: none;"><strong>Github</strong>：https://github.com/Tully-L</td>
  </tr>
    <tr>
    <td style="padding: 0px 0; font-size: 1em; border: none;"><strong>个人网站</strong>：https://tully.top/</td>
  </tr>
</table>


<h2 style="display: flex; align-items: center; color: #6aa0f0; margin-bottom: 15px; margin-top: 25px;">
  <span style="margin-right: 10px;">🛠️</span> 专业技能
</h2>

- 前端开发：React、Vue.js、TypeScript、Less/CSS3、HTML5
- 开发框架：Uniapp、Node.js、Express、Flask、MySQL、VSCode
- 开发工具：VSCode、PlatformIO、Keil、PyCharm
- 其他技能：微信小程序开发、STM32/ESP32嵌入式开发

<h2 style="display: flex; align-items: center; color: #6aa0f0; margin-bottom: 10px;">
  <span style="margin-right: 10px;">🌟</span> 核心项目经验 
</h2>

<h3><span style="color: #8aadf4; display: flex; align-items: center;"><span style="margin-right: 8px;">📱</span> 小程序开发<span style="font-size: 0.85em; font-weight: normal; margin-left: 8px; color: #666;">【独立开发】</span></span></h3>

<div style="padding-left: 2em;">
  <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;">
    <h4 style="margin: 0;">生鲜商城小程序</h4>
    <span style="font-weight: normal; color: #666; font-size: 0.9em;">2025.03-2025.04</span>
  </div>

  - <strong>技术栈</strong>：Uniapp、TS+Less、Vue、微信开发者工具
  - <strong>功能实现</strong>：商品分类展示、购物车管理、订单处理、用户登录注册、配送时间选择
  - <strong>技术亮点</strong>：组件化开发、状态管理清晰、页面过渡流畅、支持多端部署
  - <strong>项目成果</strong>：独立完成100%前端开发，提前7天完成5个核心页面；50+用户测试，UI满意度85%；深入理解Uniapp跨端开发与Vue组件通信
  - <strong>场景价值</strong>：为家庭生鲜配送定制，解决手写记账效率低、易出错问题。通过小程序实现自动记账和订单汇总，预计日处理订单200+，单笔订单处理时间从5分钟缩短至30秒，效率提升90%
</div>

<h3><span style="color: #8aadf4; display: flex; align-items: center;"><span style="margin-right: 8px;">💻</span> 网页开发项目</span></h3>

<div style="padding-left: 2em;">
  <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;">
    <h4 style="margin: 0;">天气查询网页</h4>
    <span style="font-weight: normal; color: #666; font-size: 0.9em;">2024.12</span>
  </div>

  - <strong>技术栈</strong>：React、TypeScript、Axios、OpenWeatherMap API
  - <strong>功能实现</strong>：城市天气查询、多日预报展示、地区收藏功能
  - <strong>技术亮点</strong>：响应式设计适配多种设备，API数据缓存优化加载速度
  - <strong>项目成果</strong>：面向2000+学生群体的本地化天气服务，解决传统App广告多、定位不准痛点；深入理解React组件化开发；代码作为学习笔记分享

  id: 1,
    title: '交互式校园信息展示网站',
    description: '基于React+TypeScript+Three.js的沉浸式校园数字孪生网站，完全独立开发（从对接客户到部署上线），3D地球模型入口，信息查找效率提升80%，日均访问200+。',
    highlightTerms: ['React', 'TypeScript', 'Three.js', '100%', '3D', '200+', '10分钟', '2分钟', '80%', '响应式', '独立开发'],
    image: '/images/projects/Campus00.png',
    techStack: ['React', 'TypeScript', 'Three.js', 'Leaflet', 'Tailwind CSS', 'Vite', 'Framer Motion'],
    category: 'web',
    liveUrl: 'https://greenpulsemap.com/',
    highlights: [
      'Three.js 3D地球可视化：校园建筑三维建模，支持360°全景漫游',
      '智能导航系统：Leaflet引擎实现室内外路径规划，定位精度3米级',
      '动态交互体验：Framer Motion动画过渡，操作流畅度提升50%',
      '社区共创机制：用户照片上传生成热门打卡点热力图',
      '全终端响应式：移动端适配率100%，多设备体验一致'
    ],
    achievements: [
      '功能覆盖：3D校园漫游、交互式地图导航、多校区切换、社区照片分享',
      '项目成果：完全独立开发（对接客户->部署上线），Three.js 3D渲染优化，移动端适配率100%',
      '场景价值：重构校园信息获取方式，解决传统导览效率低、信息分散问题',
      '量化收益：日均访问200+，信息查找效率提升80%，用户满意度95%'
    ],

    ]
  },
  {
    id: 2,
    title: '网球热赛事管理小程序',
    description: '基于微信小程序+Node.js+MongoDB的全链路网球赛事管理平台，实现Socket.io实时比分同步，管理效率提升87%，支持日均50+赛事。微信搜索"网球热"体验。',
    highlightTerms: ['微信小程序', 'Node.js', 'MongoDB', '100%', '5', '8', '30+', '90%', '50+', '2小时', '15分钟', '87%', 'Socket.io', '实时同步'],
    image: '/images/projects/tennis0.png',
    techStack: ['微信小程序', 'Node.js', 'Express.js', 'MongoDB', 'Socket.io', 'JWT', '微信支付API'],
    category: 'miniprogram',
    demoInfo: '微信搜索"网球热"',
    highlights: [
      'Socket.io毫秒级比分同步，观众与裁判端数据无缝衔接',
      '智能赛事推荐引擎，基于用户偏好标签匹配效率提升60%',
      '全流程自动化：报名-支付-签到-成绩公示零人工干预',
      '多端通知体系，关键节点微信模板消息触达率100%'
    ],
    achievements: [
      '功能覆盖赛事全生命周期：赛事发布、用户认证、实时比分、支付报名、社交分享',
      '项目成果：独立完成100%全栈开发，提前5天交付8个核心页面；30+用户测试UI满意度90%',
      '场景价值：重构网球赛事管理流程，解决传统模式效率低、信息不透明行业痛点',
      '量化收益：单场赛事管理时间从2小时缩短至15分钟，效率提升87%，支持日均50+赛事规模'
   
  },
  {
    id: 6,
    title: '生鲜商城小程序',
    description: '基于Uniapp+TS+Vue的生鲜购物小程序，独立完成100%前端开发，订单处理效率提升90%，支持日均200+订单。',
    highlightTerms: ['Uniapp', 'TS', 'Vue', '微信开发者工具', '100%', '7', '5', '50+', '85%', '200+', '5分钟', '30秒', '90%'],
    image: '/images/projects/veg0.png',
    techStack: ['Uniapp', 'TypeScript', 'Vue', '微信开发者工具'],
    category: 'miniprogram',
    highlights: [
      '合理分类生鲜，助顾客速找商品',
      '购物车支持查看商品、调整数量',
      '下单自动处理，流程便捷',
      '多终端适配'
    ],
    achievements: [
      '功能包括商品分类、购物车管理、订单处理、用户注册和配送时间选择',
      '项目成果：独立完成100%前端开发，提前7天完成5个核心页面；50+用户测试，UI满意度85%；深入理解Uniapp跨端开发与Vue组件通信',
      '·场景价值：为家庭生鲜配送定制，解决手写记账效率低、易出错问题',
      '预计日处理订单200+，单笔订单处理时间从5分钟缩短至30秒，效率提升90%'
    ],
  
  {
    id: 3,
    title: '学生积分管理小程序',
    description: '基于微信小程序+Node.js+MongoDB的K12家校协同管理平台，支持多媒体作业提交和智能积分体系，审核效率提升83%。',
    highlightTerms: ['微信小程序', 'Node.js', 'MongoDB', '100%', '8', '100+', '30分钟', '5分钟', '83%', '多媒体', '积分体系'],
    image: '/images/projects/stu1.png',
    techStack: ['微信小程序', 'Node.js', 'Express.js', 'MongoDB', 'JWT', 'Mongoose', '微信云存储'],
    category: 'miniprogram',
    highlights: [
      '4K多媒体作业提交：支持视频/图片/文字多格式批阅，还原线下教学体验',
      '动态积分激励系统：可视化成长曲线+徽章体系，学习参与度提升40%',
      '双角色权限架构：学生端提交追踪与家长端审核反馈精准隔离',
      '数据化成长档案：自动生成作业分析报告，支持导出存档'
    ],
    achievements: [
      '功能覆盖：用户认证、多媒体作业管理、智能积分统计、双角色权限控制',
      '项目成果：独立完成100%全栈开发，8个核心页面零BUG交付，支持双角色协同',
      '场景价值：解决传统作业管理效率低、家校沟通断层、激励机制缺失问题',
      '量化收益：日均处理作业100+，审核效率提升83%，学习主动性提升40%'
 

<h2 style="display: flex; align-items: center; color: #6aa0f0; margin-bottom: 10px;">
  <span style="margin-right: 10px;">🏆</span> 荣誉与证书
</h2>

<div style="padding-left: 1em;">
  <p style="margin-bottom: 8px; color: #333; font-size: 0.95em; line-height: 1.4;">
    <span style="font-weight: 600; color: #333;">技能证书：</span>工业互联网平台开发工程师（初级）、CET-6（英语六级）、普通话国家二级甲等证书
  </p>
  
  <p style="margin-bottom: 8px; color: #333; font-size: 0.95em; line-height: 1.4;">
    <span style="font-weight: 600; color: #333;">获奖经历：</span>中国大学生计算机设计大赛省二（2025.5）、蓝桥杯Web应用开发大学组三等奖（2024.4）、四川高校阅读文化节"阅读之星"（连续三年2023-2025）
  </p>
  
  <p style="margin-bottom: 8px; color: #333; font-size: 0.95em; line-height: 1.4;">
    <span style="font-weight: 600; color: #333;">校园经历：</span>担任西华大学小球协会会长（2024.9-至今）、获院级三好学生（2023.11）、"三下乡"社会实践活动优秀个人奖（2023.12）
  </p>
  

</div>

