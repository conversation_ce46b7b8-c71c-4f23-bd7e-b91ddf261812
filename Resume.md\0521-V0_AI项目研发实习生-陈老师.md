# <span style="color: #3388ff;">个人简历</span>

## <span style="color: #3388ff;">👩🏻‍💻 个人信息</span>

- **姓名**：林雨
- **教育背景**：西华大学 | 物联网工程（2022.09—2026.06）| 本科在读
- **主修课程**：Web前/后端开发技术、数据结构与算法设计、计算机网络、数据库原理与应用
- **求职意向**：AI实习生（机器学习 | 计算机视觉 | 深度学习等）
- **期望地点**：宜宾市 | 可到岗时间:2025年5月 | 实习时长:12个月
- **联系方式**：(+86) 177-8181-1549 | <EMAIL>
- **Github**：https://github.com/Tully-L



## <span style="color: #3388ff;">🚀 项目经验</span>
### <span style="color: #8aadf4;">🧠 人工智能项目</span>

<div style="display: flex; justify-content: space-between; align-items: center; margin-top: 10px; margin-bottom: 5px;">
  <h4 style="margin: 0;">🛄 慧眸智检——基于YOLO-Xray的智能行李安检系统（获省级竞赛二等奖）</h4>
  <span style="font-weight: normal; color: #666; font-size: 0.9em;">2025/03-2025/04</span>
</div>

- **项目背景**：解决传统安检漏检率5%、误报率10-15%的痛点，开发智能违禁品检测系统。
- **技术栈**：Python + PyTorch + YOLOv8 + OpenCV + Flask
- **算法优化**：引入空间/通道注意力模块，提升PIDray数据集mAP50-95至79.7%(+12.3%)，OPIxray达96.8%(+5.9%)。
- **损失函数设计**：开发EdgeOBB损失函数，解决遮挡定位问题，提升Hard场景mAP75 6.5%。
- **模型部署**：基于Flask部署Web检测平台，实现150FPS实时检测。
- **团队协作**：与3名同学合作完成，我负责数据集处理与增强、前端界面开发、算法评估与测试。


### **🐱 猫爪印掌阅——沉浸式文学体验智能体系统**  2025/04-2025/05
- **项目背景**：解决用户阅读碎片化、文学理解难问题，开发AI文学交互系统。  
- **技术栈**：Python + Coze平台 + 检索增强生成(RAG) + 知识库构建技术  
- **智能体设计**：构建30+作家人格模型，应用提示词工程实现85%作家语言风格还原。
- **现实关联模块**：开发古今联动视角，实现文学家评析现实话题，用户共鸣度提升58%。  
- **解读系统开发**：设计5种核心视角解读框架，覆盖120部经典作品，提升用户理解深度46%。  
- **团队协作**：与1名组员合作完成，我负责智能体设计与提示词工程、知识库构建、工作流设计及RAG系统实现。

## <span style="color: #3388ff;">🛠️ 专业技能</span>

**编程语言**
- **Python**（熟练，精通数据处理与算法实现，熟悉Pandas/NumPy/Matplotlib）
- **Java/C++**（良好，掌握基础语法与面向对象编程）
- **JavaScript/TypeScript**（熟练，能独立开发前端应用）

**AI框架与工具**
- **计算机视觉**：YOLO系列模型应用，熟悉图像检测流程与评估方法
- **深度学习**：PyTorch（熟练使用，能构建CNN并进行训练、调优）
- **数据分析**：熟练使用Python数据分析库进行数据可视化与特征工程
- **AI应用开发**：提示词工程、智能体交互设计、检索增强生成(RAG)
- **AI工具应用**：熟练使用Claude、Gemini等AI工具，精通Agent模式开发应用

**其他技能**
- **数据库技术**：MySQL/SQLite，熟练编写SQL语句，掌握数据管理
- **前端开发**：React、Vue.js、TypeScript、Less/CSS3、HTML5
- **后端开发**：Flask框架应用，构建简单Web服务
- **嵌入式开发**：熟悉STM32F103/STM32L4系列单片机开发、ESP32开发、传感器应用及通信协议
- **版本控制**：Git，能熟练使用进行协作开发

其他项目
### <span style="color: #8aadf4;">🤖 嵌入式系统开发<span style="font-size: 0.85em; font-weight: normal; margin-left: 8px; color: #666;">【独立开发】</span></span></span>

<div style="display: flex; justify-content: space-between; align-items: center; margin-top: 10px; margin-bottom: 5px;">
  <h4 style="margin: 0;">🔒 智能门锁与环境检测系统</h4>
  <span style="font-weight: normal; color: #666; font-size: 0.9em;">2025/01</span>
</div>

- **硬件开发**：基于STM32单片机设计电路，集成LED、温湿度/光照传感器、OLED屏幕和舵机
- **软件实现**：编写C语言程序实现数据采集、显示、存储、报警和人机交互功能
- **系统特点**：
  - 实现了完整的环境监测功能，代码结构清晰，模块化程度高，便于维护和扩展。

<div style="display: flex; justify-content: space-between; align-items: center; margin-top: 10px; margin-bottom: 5px;">
  <h4 style="margin: 0;">🚗 STM32F03C8T6智能小车开发</h4>
  <span style="font-weight: normal; color: #666; font-size: 0.9em;">2024/12</span>
</div>

- **功能实现**：红外循迹、避障、遥控功能



### <span style="color: #8aadf4;">🗄️ 数据库应用开发<span style="font-size: 0.85em; font-weight: normal; margin-left: 8px; color: #666;">【独立开发】</span></span></span>

<div style="display: flex; justify-content: space-between; align-items: center; margin-top: 10px; margin-bottom: 5px;">
  <h4 style="margin: 0;">🗂️ 失物招领系统(MFC框架)</h4>
  <span style="font-weight: normal; color: #666; font-size: 0.9em;">2024/03-2024/04</span>
</div>

- **技术栈**：C++ + MFC框架 + MySQL数据库
- **核心功能**：失物信息登记、招领信息发布、关键词搜索匹配
- **数据库设计**：设计合理的数据表结构，实现高效数据存储与查询
- **技术亮点**：利用MFC框架简化Windows应用开发，结合MySQL实现高效数据管理

<div style="display: flex; justify-content: space-between; align-items: center; margin-top: 10px; margin-bottom: 5px;">
  <h4 style="margin: 0;">📊 大数据分析与挖掘项目</h4>
  <span style="font-weight: normal; color: #666; font-size: 0.9em;">2024/11</span>
</div>

- **技术栈**：Python + NumPy + Pandas + Matplotlib + PyTorch
- **算法实现**：
  - 开发PCA降维算法，实现人脸识别100%准确率。
  - 实现Apriori与FP-Growth算法，生成商品摆放优化策略。
  - 构建ID3、贝叶斯、KNN分类模型，应用K-Means聚类算法。
  - 使用PyTorch搭建CNN网络，达成99.01%测试准确率。
- **问题解决能力**：解决中文字体显示异常、数据下载失败等技术问题，确保实验顺利进行

<h3><span style="color: #8aadf4; display: flex; align-items: center;"><span style="margin-right: 8px;">📱</span> 小程序开发<span style="font-size: 0.85em; font-weight: normal; margin-left: 8px; color: #666;">【独立开发】</span></span></h3>

<div style="padding-left: 2em;">
  <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;">
    <h4 style="margin: 0;"><span style="margin-right: 8px;">🥬</span>生鲜商城小程序</h4>
    <span style="font-weight: normal; color: #666; font-size: 0.9em;">2025.03-2025.04</span>
  </div>

  - <strong>技术栈</strong>：Uniapp + TS + Less + Vue + 微信开发者工具
  - <strong>功能实现</strong>：商品分类展示、购物车管理、订单处理、用户登录注册、配送时间选择
  - <strong>技术亮点</strong>：组件化开发、状态管理清晰、页面过渡流畅、支持多端部署
  - <strong>项目成果</strong>：独立完成100%前端开发，提前7天完成5个核心页面；50+用户测试，UI满意度85%；深入理解Uniapp跨端开发与Vue组件通信
  - <strong>场景价值</strong>：为家庭生鲜配送定制，解决手写记账效率低、易出错问题。通过小程序实现自动记账和订单汇总，预计日处理订单200+，单笔订单处理时间从5分钟缩短至30秒，效率提升90%
</div>

<h3><span style="color: #8aadf4; display: flex; align-items: center;"><span style="margin-right: 8px;">💻</span>  网页开发项目<span style="font-size: 0.85em; font-weight: normal; margin-left: 8px; color: #666;">【独立开发】</span></h3>

  <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 10px; margin-bottom: 5px;">
    <h4 style="margin: 0;">📝 React TodoList 应用</h4>
    <span style="font-weight: normal; color: #666; font-size: 0.9em;">2024.11</span>
  </div>

  - <strong>技术栈</strong>：React + TypeScript + Redux + CSS Modules
  - <strong>功能实现</strong>：任务的添加/编辑/删除、状态管理、分类筛选
  - <strong>技术亮点</strong>：组件化开发提高代码复用性，TypeScript强类型确保代码质量
  - <strong>项目成果</strong>：2周完成初版开发，掌握Redux状态管理；作为TypeScript学习实践项目

  <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 10px; margin-bottom: 5px;">
    <h4 style="margin: 0;">🧮 拟物3D计算器</h4>
    <span style="font-weight: normal; color: #666; font-size: 0.9em;">2024.11</span>
  </div>

  - <strong>技术栈</strong>：React + styled-components + JavaScript
  - <strong>功能实现</strong>：支持基本四则运算、清除与退格操作
  - <strong>技术亮点</strong>：使用styled-components实现独特3D按键效果，提升交互体验
  - <strong>项目成果</strong>：独立完成所有UI组件设计，课程作业获A评级；优化按键交互体验

<h2 style="display: flex; align-items: center; color: #6aa0f0; margin-bottom: 10px;">
  <span style="margin-right: 10px;">🏆</span> 荣誉与证书
</h2>

<div style="padding-left: 1em;">
  <p style="margin-bottom: 8px; color: #333; font-size: 0.95em; line-height: 1.4;">
    <span style="font-weight: 600; color: #333;">技能证书：</span>工业互联网平台开发工程师（初级）、CET-6（英语六级）、普通话国家二级甲等证书
  </p>
  
  <p style="margin-bottom: 8px; color: #333; font-size: 0.95em; line-height: 1.4;">
    <span style="font-weight: 600; color: #333;">获奖经历：</span>中国大学生计算机设计大赛省二（2025.5）、蓝桥杯Web应用开发大学组三等奖（2024.4）、四川高校阅读文化节"阅读之星"（连续三年2023-2025）
  </p>
  
  <p style="margin-bottom: 8px; color: #333; font-size: 0.95em; line-height: 1.4;">
    <span style="font-weight: 600; color: #333;">校园经历：</span>担任西华大学小球协会会长（2024.9-至今）、获院级三好学生（2023.11）、"三下乡"社会实践活动优秀个人奖（2023.12）
  </p>
  

