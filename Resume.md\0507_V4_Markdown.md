<style>
h2 {
  font-size: 1.4em !important;
  margin-bottom: 4px !important;
  margin-top: 7px !important;
}
h3 {
  font-size: 1.2em !important;
  margin-bottom: 1px !important;
  margin-top: 4px !important;
}
h4 {
  font-size: 1.1em !important;
  margin-bottom: 1px !important;
  margin-top: 1px !important;
}
p, li, span {
  font-size: 0.95em !important;
  line-height: 1.25 !important;
  margin-bottom: 2px !important;
  margin-top: 2px !important;
}
div {
  margin: 2px 0 !important;
  padding-bottom: 2px !important;
}
ul {
  margin-top: 3px !important;
  margin-bottom: 3px !important;
  padding-top: 0 !important;
}
</style>

<h2 style="display: flex; align-items: center; color: #2b7de9; margin-bottom: 10px;">
  <span style="margin-right: 10px;">👩‍💻</span> 林雨 | 前端开发工程师
</h2>


- 学校专业：西华大学 | 物联网工程
- 求职意向：Web前端开发工程师
- 电话：(+86) 17781811549
- 邮箱：<EMAIL>
- Github: https://github.com/Tully-L

<h2 style="display: flex; align-items: center; color: #2b7de9; margin-bottom: 10px;">
  <span style="margin-right: 10px;">🛠️</span> 专业技能
</h2>

- 开发工具：VSCode、PlatformIO、Keil、PyCharm
- 前端开发：React、Vue.js、TypeScript、Less/CSS3、HTML5
- 开发工具：Uniapp、Node.js、Express、Flask、MySQL、VSCode
- 其他技能：AI应用开发、STM32/ESP32嵌入式开发

<h2 style="display: flex; align-items: center; color: #2b7de9; margin-bottom: 10px;">
  <span style="margin-right: 10px;">🌟</span> 核心项目经验 
</h2>

<h3><span style="color: #4a8af4; display: flex; align-items: center;"><span style="margin-right: 8px;">📱</span> 小程序开发<span style="font-size: 0.85em; font-weight: normal; margin-left: 8px; color: #666;">【独立开发】</span></span></h3>
<div style="padding-left: 2em;"><h4>生鲜商城小程序 <span style="font-weight: normal; color: #666; font-size: 0.9em;">(2025.03-2025.04)</span></h4>

- 技术栈：Uniapp、TS+Less、Vue、微信开发者工具
- 功能实现：商品分类展示、购物车管理、订单处理、用户登录注册、配送时间选择
- 技术亮点：组件化开发、状态管理清晰、页面过渡流畅、支持多端部署
</div>
<h3><span style="color: #4a8af4; display: flex; align-items: center;"><span style="margin-right: 8px;">💻</span> 网页开发项目</span></h3>
<div style="padding-left: 2em;"><h4>天气查询网页 / React TodoList 应用<span style="font-weight: normal; color: #666; font-size: 0.9em;">(2024.12)</span></h4>

- 技术栈：React、TypeScript、Redux、Axios、OpenWeatherMap API
- 功能实现：城市天气查询与预报展示、任务的添加/编辑/删除及状态管理
- 技术亮点：组件化开发提高代码复用性，TypeScript强类型确保代码质量
<h4>飞机大战游戏<span style="font-weight: normal; color: #666; font-size: 0.9em;">(2024.10)</span></h4>

- 技术栈：HTML5 Canvas、JavaScript、CSS3
- 功能实现：飞机控制、碰撞检测、关卡设计、计分系统
- 技术亮点：Canvas动画效果流畅，游戏逻辑与渲染分离
</div>
<h3><span style="color: #4a8af4; display: flex; align-items: center;"><span style="margin-right: 8px;">🔌</span> 嵌入式系统开发<span style="font-size: 0.85em; font-weight: normal; margin-left: 8px; color: #666;">【独立开发】</span></span></h3>
<div style="padding-left: 2em;"><h4>智能门锁与环境检测系统 <span style="font-weight: normal; color: #666; font-size: 0.9em;">(2024.12-2025.01)</span></h4>

- 基于STM32F03C8T6开发，集成多种传感器，实现数据采集、显示与报警功能
- 代码结构清晰，模块化程度高，便于维护和扩展
</div>

<h2 style="display: flex; align-items: center; color: #2b7de9; margin-bottom: 10px;">
  <span style="margin-right: 10px;">🏆</span> 荣誉与证书
</h2>
<span style="font-weight: 600;">

- 蓝桥杯Web应用开发大学组三等奖
- 工业互联网平台开发工程师初级
- 普通话国家二级甲等证书
- 英语六级证书

</span>


