<style>
h1, h2, h3, h4, p, li, span, td {
  font-family: "微软雅黑", "Microsoft YaHei", "苹方", "PingFang SC", sans-serif !important;
}
h2 {
  font-size: 1.4em !important;
  margin-bottom: 4px !important;
  margin-top: 7px !important;
}
h3 {
  font-size: 1.2em !important;
  margin-bottom: 1px !important;
  margin-top: 4px !important;
}
h4 {
  font-size: 1.1em !important;
  margin-bottom: 1px !important;
  margin-top: 1px !important;
}
p, li, span {
  font-size: 0.95em !important;
  line-height: 1.25 !important;
  margin-bottom: 2px !important;
  margin-top: 2px !important;
}
div {
  margin: 2px 0 !important;
  padding-bottom: 2px !important;
}
ul {
  margin-top: 3px !important;
  margin-bottom: 3px !important;
  padding-top: 0 !important;
}
strong {
  font-weight: 550;

}
</style>

<h1 style="display: flex; align-items: center; color: #6aa0f0; margin-bottom: 15px; margin-top: 20px;">
  <span style="margin-right: 10px;">👩‍💻</span> 林雨 | 前端开发工程师
</h1>

<table style="border-collapse: separate; border-spacing: 0 3px; width: 100%; margin-bottom: 8px; border: none;">
  <tr>
    <td style="padding: 0px 0; font-size: 1em; border: none;"><strong>求职意向</strong>：前端开发工程师</td>
  </tr>
  <tr>
    <td style="padding: 0px 0; font-size: 1em; border: none;"><strong>教育背景</strong>：西华大学 | 物联网工程（2022.09—2026.06）</td>
  </tr>
  <tr>
    <td style="padding: 0px 0; font-size: 1em; border: none;"><strong>主修课程</strong>：Web前/后端开发技术、数据结构与算法设计、计算机网络、数据库原理与应用</td>
  </tr>
  <tr>
    <td style="padding: 0px 0; font-size: 1em; border: none;"><strong>联系方式</strong>：(+86) 177-8181-1549 | <EMAIL></td>
  </tr>
  <tr>
    <td style="padding: 0px 0; font-size: 1em; border: none;"><strong>Github</strong>：https://github.com/Tully-L</td>
  </tr>
</table>


<h2 style="display: flex; align-items: center; color: #6aa0f0; margin-bottom: 15px; margin-top: 25px;">
  <span style="margin-right: 10px;">🛠️</span> 专业技能
</h2>

- 前端开发：React、Vue.js、TypeScript、Less/CSS3、HTML5
- 开发框架：Uniapp、Node.js、Express、Flask、MySQL、VSCode
- 开发工具：VSCode、PlatformIO、Keil、PyCharm
- 其他技能：微信小程序开发、STM32/ESP32嵌入式开发

<h2 style="display: flex; align-items: center; color: #6aa0f0; margin-bottom: 10px;">
  <span style="margin-right: 10px;">🌟</span> 核心项目经验 
</h2>

<h3><span style="color: #8aadf4; display: flex; align-items: center;"><span style="margin-right: 8px;">📱</span> 小程序开发<span style="font-size: 0.85em; font-weight: normal; margin-left: 8px; color: #666;">【独立开发】</span></span></h3>

<div style="padding-left: 2em;">
  <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;">
    <h4 style="margin: 0;">生鲜商城小程序</h4>
    <span style="font-weight: normal; color: #666; font-size: 0.9em;">2025.03-2025.04</span>
  </div>

  - <strong>技术栈</strong>：Uniapp、TS+Less、Vue、微信开发者工具
  - <strong>功能实现</strong>：商品分类展示、购物车管理、订单处理、用户登录注册、配送时间选择
  - <strong>技术亮点</strong>：组件化开发、状态管理清晰、页面过渡流畅、支持多端部署
  - <strong>项目成果</strong>：独立完成100%前端开发，提前7天完成5个核心页面；50+用户测试，UI满意度85%；深入理解Uniapp跨端开发与Vue组件通信
  - <strong>场景价值</strong>：为家庭生鲜配送定制，解决手写记账效率低、易出错问题。通过小程序实现自动记账和订单汇总，预计日处理订单200+，单笔订单处理时间从5分钟缩短至30秒，效率提升90%
</div>

<h3><span style="color: #8aadf4; display: flex; align-items: center;"><span style="margin-right: 8px;">💻</span> 网页开发项目</span></h3>

<div style="padding-left: 2em;">
  <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;">
    <h4 style="margin: 0;">天气查询网页</h4>
    <span style="font-weight: normal; color: #666; font-size: 0.9em;">2024.12</span>
  </div>

  - <strong>技术栈</strong>：React、TypeScript、Axios、OpenWeatherMap API
  - <strong>功能实现</strong>：城市天气查询、多日预报展示、地区收藏功能
  - <strong>技术亮点</strong>：响应式设计适配多种设备，API数据缓存优化加载速度
  - <strong>项目成果</strong>：面向2000+学生群体的本地化天气服务，解决传统App广告多、定位不准痛点；深入理解React组件化开发；代码作为学习笔记分享

  <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 10px; margin-bottom: 5px;">
    <h4 style="margin: 0;">React TodoList 应用</h4>
    <span style="font-weight: normal; color: #666; font-size: 0.9em;">2024.11</span>
  </div>

  - <strong>技术栈</strong>：React、TypeScript、Redux、CSS Modules
  - <strong>功能实现</strong>：任务的添加/编辑/删除、状态管理、分类筛选
  - <strong>技术亮点</strong>：组件化开发提高代码复用性，TypeScript强类型确保代码质量
  - <strong>项目成果</strong>：2周完成初版开发，掌握Redux状态管理；作为TypeScript学习实践项目

  <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 10px; margin-bottom: 5px;">
    <h4 style="margin: 0;">拟物3D计算器</h4>
    <span style="font-weight: normal; color: #666; font-size: 0.9em;">2024.11</span>
  </div>

  - <strong>技术栈</strong>：React、styled-components、JavaScript
  - <strong>功能实现</strong>：支持基本四则运算、清除与退格操作
  - <strong>技术亮点</strong>：使用styled-components实现独特3D按键效果，提升交互体验
  - <strong>项目成果</strong>：独立完成所有UI组件设计，课程作业获A评级；优化按键交互体验

 <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 10px; margin-bottom: 5px;">
    <h4 style="margin: 0;">飞机大战游戏</h4>
    <span style="font-weight: normal; color: #666; font-size: 0.9em;">2024.10</span>
  </div>

  - <strong>技术栈</strong>：HTML5 Canvas、JavaScript、CSS3
  - <strong>功能实现</strong>：飞机控制、碰撞检测、关卡设计、计分系统
  - <strong>技术亮点</strong>：Canvas动画效果流畅，游戏逻辑与渲染分离
  - <strong>项目成果</strong>：实现3种敌机类型，代码复用率70%；10名同学参与测试；深入理解Canvas动画原理
</div>

<h2 style="display: flex; align-items: center; color: #6aa0f0; margin-bottom: 10px;">
  <span style="margin-right: 10px;">🏆</span> 荣誉与证书
</h2>

<div style="padding-left: 1em;">
  <p style="margin-bottom: 8px; color: #333; font-size: 0.95em; line-height: 1.4;">
    <span style="font-weight: 600; color: #333;">技能证书：</span>工业互联网平台开发工程师（初级）、CET-6（英语六级）、普通话国家二级甲等证书
  </p>
  
  <p style="margin-bottom: 8px; color: #333; font-size: 0.95em; line-height: 1.4;">
    <span style="font-weight: 600; color: #333;">获奖经历：</span>蓝桥杯Web应用开发大学组三等奖（2024.4）、四川高校阅读文化节"阅读之星"（连续三年2023-2025）
  </p>
  
  <p style="margin-bottom: 8px; color: #333; font-size: 0.95em; line-height: 1.4;">
    <span style="font-weight: 600; color: #333;">校园经历：</span>担任西华大学小球协会会长（2024.9-至今）、获院级三好学生（2023.11）、"三下乡"社会实践活动优秀个人奖（2023.12）
  </p>
  

</div>

