<style>
h2 {
  font-size: 1.4em !important;
  margin-bottom: 4px !important;
  margin-top: 14px !important;
}
h3 {
  font-size: 1.2em !important;
  margin-bottom: 3px !important;
  margin-top: 8px !important;
}
h4 {
  font-size: 1.1em !important;
  margin-bottom: 2px !important;
  margin-top: 5px !important;
}
p, li, span {
  font-size: 0.95em !important;
  line-height: 1.25 !important;
  margin-bottom: 2px !important;
  margin-top: 2px !important;
}
div {
  margin: 9px 0 !important;
  padding-bottom: 5px !important;
}
ul {
  margin-top: 3px !important;
  margin-bottom: 3px !important;
  padding-top: 0 !important;
}
</style>

<h2 style="display: flex; align-items: center; color: #2b7de9; margin-bottom: 10px;">
  <span style="margin-right: 10px;">👩‍💻</span> 林雨 | 前端开发工程师简历
</h2>

<div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 15px;">
  <div style="width: 70%;">
    <p style="margin: 4px 0;">- 教育经历：2022-2026 西华大学 | 物联网工程</p>
    <p style="margin: 4px 0;">- 联系方式：(+86) 177-8181-1549 | <EMAIL></p>
    <p style="margin: 4px 0;">- Github：https://github.com/Tully-L</p>
    <p style="margin: 4px 0;">- 期望城市：成都 | 重庆</p>
  </div>
  
  <div style="width: 25%; text-align: right;">
    <img src="./Pic.jpg" alt="个人照片" style="width: 100%; height: auto; border-radius: 4px; object-fit: cover;">
  </div>
</div>

<h2 style="display: flex; align-items: center; color: #2b7de9; margin-bottom: 10px;">
  <span style="margin-right: 10px;">🛠️</span> 专业技能
</h2>

- 前端开发：React、Vue.js、TypeScript、Less、CSS3、HTML5
- 小程序开发：Uniapp、微信开发者工具
- 后端技术：Node.js、Express、Flask
- 数据库：MySQL、SQLite
- AI应用：大模型应用及API接口开发
- 嵌入式开发：STM32及ESP32单片机、传感器应用、通信协议
- 开发工具：VSCode、PlatformIO、Keil、PyCharm

<h2 style="display: flex; align-items: center; color: #2b7de9; margin-bottom: 10px;">
  <span style="margin-right: 10px;">🌟</span> 核心项目经验
</h2>

### <span style="color: #4a8af4; display: flex; align-items: center;"><span style="margin-right: 8px;">📱</span> 小程序开发</span>
#### 生鲜商城小程序
- 技术栈：Uniapp、TS+Less、Vue、微信开发者工具
- 功能实现：商品分类展示、购物车管理、订单处理、用户登录注册、配送时间选择
- 技术亮点：组件化开发、状态管理清晰、页面过渡流畅、支持多端部署

### <span style="color: #4a8af4; display: flex; align-items: center;"><span style="margin-right: 8px;">💻</span> 网页开发项目</span>

#### 天气查询网页
- 技术栈：React、Axios、OpenWeatherMap API、CSS3
- 功能实现：城市天气信息查询、未来天气预报展示
- 技术亮点：通过API调用获取实时天气数据，实现响应式设计
#### 飞机大战游戏
- 技术栈：HTML5 Canvas、JavaScript、CSS3
- 功能实现：飞机移动控制、射击与碰撞检测、关卡难度设计、计分系统
- 技术亮点：使用Canvas技术绘制游戏画面，实现流畅的动画效果
#### React TodoList 应用
- 技术栈：React、TypeScript、Redux、Vite
- 功能实现：实现任务的添加、编辑、删除及状态管理，支持列表筛选与排序
- 技术亮点：采用组件化开发模式提高代码复用性，使用TypeScript强类型确保代码质量
#### 拟物3D计算器
- 技术栈：React、styled-components、JavaScript
- 功能实现：支持基本四则运算、清除与退格操作
- 技术亮点：使用styled-components实现独特3D按键效果，提升交互体验

<h2 style="display: flex; align-items: center; color: #2b7de9; margin-bottom: 10px;">
  <span style="margin-right: 10px;">🏆</span> 荣誉与证书
</h2>

- 蓝桥杯Web应用开发大学组三等奖
- 工业互联网平台开发工程师初级
- 英语六级证书
- 普通话国家二级甲等证书

<h2 style="display: flex; align-items: center; color: #2b7de9; margin-bottom: 10px;">
  <span style="margin-right: 10px;">📂</span> 更多项目经验
</h2>

### <span style="color: #4a8af4; display: flex; align-items: center;"><span style="margin-right: 8px;">🔌</span> 嵌入式系统开发</span>
#### 智能门锁与环境检测系统
- 基于STM32F03C8T6开发，集成多种传感器，实现数据采集、显示与报警功能
- 代码结构清晰，模块化程度高，便于维护和扩展
