# <span style="color: #3388ff;">个人简历</span>

## <span style="color: #3388ff;">👩🏻‍💻 个人信息</span>

- **姓名**：林雨
- **教育背景**：西华大学 | 物联网工程（2022.09—2026.06）
- **主修课程**：Web前/后端开发技术、数据结构与算法设计、计算机网络、数据库原理与应用
- **联系方式**：(+86) 177-8181-1549 | <EMAIL>
- **Github**：https://github.com/Tully-L

## <span style="color: #3388ff;">🛠️ 专业技能</span>

- **编程语言**：Python、Java、JavaScript、TypeScript、C/C++
- **数据库技术**：了解MySQL、SQLite
- **AI应用开发**：熟练使用Claude、、Gemini、豆包、DeepSeek等大模型及API接口
- **数据分析**：熟练使用Python及相关库（NumPy、pandas、Matplotlib）进行数据分析
- **嵌入式开发**：熟悉STM32F103/STM32L4系列单片机开发、ESP32开发、传感器应用及通信协议
- **后端开发**：掌握Node.js、Express、Flask框架，了解后端接口开发
- **前端开发**：React、Vue.js、TypeScript、Less/CSS3、HTML5
- **开发工具**：VSCode、PlatformIO IDE、Arduino IDE、Keil、STM32CubeMX、PyCharm、Idea等开发环境

## <span style="color: #3388ff;">🚀 项目经验</span>

### <span style="color: #8aadf4;">🤖 嵌入式系统开发</span>

<div style="display: flex; justify-content: space-between; align-items: center; margin-top: 10px; margin-bottom: 5px;">
  <h4 style="margin: 0;">🔒 智能门锁与环境检测系统</h4>
  <span style="font-weight: normal; color: #666; font-size: 0.9em;">2025/01</span>
</div>

- **硬件开发**：基于STM32单片机设计电路，集成LED、温湿度/光照传感器、OLED屏幕和舵机
- **软件实现**：编写C语言程序实现数据采集、显示、存储、报警和人机交互功能
- **系统特点**：
  - 实现了完整的环境监测功能，代码结构清晰，模块化程度高，便于维护和扩展。

<div style="display: flex; justify-content: space-between; align-items: center; margin-top: 10px; margin-bottom: 5px;">
  <h4 style="margin: 0;">🚗 STM32F03C8T6智能小车开发</h4>
  <span style="font-weight: normal; color: #666; font-size: 0.9em;">2024/12</span>
</div>

- **功能实现**：红外循迹、避障、遥控功能

### <span style="color: #8aadf4;">🧠 人工智能项目</span>

<div style="display: flex; justify-content: space-between; align-items: center; margin-top: 10px; margin-bottom: 5px;">
  <h4 style="margin: 0;">🛄 慧眸智检——基于YOLO-Xray的智能行李安检系统</h4>
  <span style="font-weight: normal; color: #666; font-size: 0.9em;">2025/03-2025/04</span>
</div>

- **后端开发**：使用Flask框架构建RESTful API，实现与前端的数据交互
- **AI模型集成**：调用预训练的YOLO-Xray模型，实现图片、视频及实时检测功能
- **数据处理**：使用Python处理检测结果数据，优化网络请求流程和数据缓存机制
- **系统优化**：降低检测延迟，提升系统响应速度，实现直观的检测结果可视化展示
- **项目成果**：系统支持多种形式的行李检测需求，提高安检效率与准确性

<div style="display: flex; justify-content: space-between; align-items: center; margin-top: 10px; margin-bottom: 5px;">
  <h4 style="margin: 0;">📊 大数据分析与挖掘项目</h4>
  <span style="font-weight: normal; color: #666; font-size: 0.9em;">2024/11</span>
</div>

- **算法实现**：
  - 主成分分析(PCA)：使用Python实现数据降维和人脸识别，准确率达100%
  - 关联规则挖掘：实现Apriori和FP-Growth算法，提出商品摆放策略
  - 分类与聚类算法：实现ID3、朴素贝叶斯、KNN等算法，掌握K-Means聚类
  - 神经网络：使用PyTorch构建CNN，测试准确率达99.01%
- **问题解决能力**：解决中文字体显示异常、数据下载失败等技术问题，确保实验顺利进行

### <span style="color: #8aadf4;">🗄️ 数据库应用开发</span>

<div style="display: flex; justify-content: space-between; align-items: center; margin-top: 10px; margin-bottom: 5px;">
  <h4 style="margin: 0;">🗂️ 失物招领系统(MFC框架)</h4>
  <span style="font-weight: normal; color: #666; font-size: 0.9em;">2024/03-2024/04</span>
</div>

- **技术栈**：C++、MFC框架、MySQL数据库
- **核心功能**：失物信息登记、招领信息发布、关键词搜索匹配
- **数据库设计**：设计合理的数据表结构，实现高效数据存储与查询
- **技术亮点**：利用MFC框架简化Windows应用开发，结合MySQL实现高效数据管理

<h3><span style="color: #8aadf4; display: flex; align-items: center;"><span style="margin-right: 8px;">📱</span>小程序开发<span style="font-size: 0.85em; font-weight: normal; margin-left: 8px; color: #666;">【独立开发】</span></span></h3>

<div style="padding-left: 2em;">
  <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;">
    <h4 style="margin: 0;">生鲜商城小程序</h4>
    <span style="font-weight: normal; color: #666; font-size: 0.9em;">2025.03-2025.04</span>
  </div>

  - <strong>技术栈</strong>：Uniapp、TS+Less、Vue、微信开发者工具
  - <strong>功能实现</strong>：商品分类展示、购物车管理、订单处理、用户登录注册、配送时间选择
  - <strong>技术亮点</strong>：组件化开发、状态管理清晰、页面过渡流畅、支持多端部署
  - <strong>项目成果</strong>：独立完成100%前端开发，提前7天完成5个核心页面；50+用户测试，UI满意度85%；深入理解Uniapp跨端开发与Vue组件通信
  - <strong>场景价值</strong>：为家庭生鲜配送定制，解决手写记账效率低、易出错问题。通过小程序实现自动记账和订单汇总，预计日处理订单200+，单笔订单处理时间从5分钟缩短至30秒，效率提升90%
</div>

<h3><span style="color: #8aadf4; display: flex; align-items: center;"><span style="margin-right: 8px;">💻</span>  网页开发项目</span></h3>

  <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 10px; margin-bottom: 5px;">
    <h4 style="margin: 0;">📝 React TodoList 应用</h4>
    <span style="font-weight: normal; color: #666; font-size: 0.9em;">2024.11</span>
  </div>

  - <strong>技术栈</strong>：React、TypeScript、Redux、CSS Modules
  - <strong>功能实现</strong>：任务的添加/编辑/删除、状态管理、分类筛选
  - <strong>技术亮点</strong>：组件化开发提高代码复用性，TypeScript强类型确保代码质量
  - <strong>项目成果</strong>：2周完成初版开发，掌握Redux状态管理；作为TypeScript学习实践项目

  <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 10px; margin-bottom: 5px;">
    <h4 style="margin: 0;">🧮 拟物3D计算器</h4>
    <span style="font-weight: normal; color: #666; font-size: 0.9em;">2024.11</span>
  </div>

  - <strong>技术栈</strong>：React、styled-components、JavaScript
  - <strong>功能实现</strong>：支持基本四则运算、清除与退格操作
  - <strong>技术亮点</strong>：使用styled-components实现独特3D按键效果，提升交互体验
  - <strong>项目成果</strong>：独立完成所有UI组件设计，课程作业获A评级；优化按键交互体验

夸大！！！吹牛一点 图形化界面（又感觉不靠谱 怎么人都这么爱吹牛呢）